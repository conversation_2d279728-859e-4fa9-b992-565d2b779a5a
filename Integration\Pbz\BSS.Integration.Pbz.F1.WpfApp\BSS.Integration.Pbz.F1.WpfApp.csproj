<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <UseWPF>true</UseWPF>
    <RootNamespace>BSS.Integration.Pbz.F1.WpfApp</RootNamespace>
    <AssemblyName>BSS.Integration.Pbz.F1.WpfApp</AssemblyName>
    <LangVersion>latest</LangVersion>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\BSS.Integration.Pbz.F1\BSS.Integration.Pbz.F1.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BSS.Framework.Logging" Version="1.0.0.3" />
    <PackageReference Include="BSS.Framework.Wcf" Version="2.0.3" />
    <PackageReference Include="log4net" Version="2.0.15" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="Prism.DryIoc" Version="8.1.97" />
  </ItemGroup>

  <!-- Shared imports, if needed -->
  <Import Project="..\..\BSS.SessionLogger\BSS.SessionLogger.projitems" Label="Shared" />

</Project>
