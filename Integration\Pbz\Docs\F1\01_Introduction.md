# Croatian Fiscalization Web Service - Introduction

## Overview

This documentation covers the Croatian Fiscalization Web Service APIs that need to be integrated into the Hyperion application. The service is being implemented to comply with the new Croatian Fiscalization Law (Zakon o fiskalizaciji) that comes into effect on January 1, 2026.

## Business Context

Starting from January 1, 2026, all invoices issued to domestic individuals (B2C) in Croatia must be fully fiscalized, regardless of the payment method. This includes invoices that contain VAT (PDV) and are issued by brokerage services.

### Key Requirements

- All invoices containing at least one item with a VAT rate greater than 0% must be fiscalized
- Invoices with all items having 0% VAT rate do not require fiscalization
- Fiscalized invoices must receive:
  - JIR (Jedinstveni identifikator ra�una) - Unique invoice identifier
  - ZKI (Za�titni kod izdavatelja) - Issuer protection code
  - QR code for verification

### Mandatory Invoice Elements

Every fiscalized invoice must contain:
1. Date and time of issue
2. Invoice number (format: invoice_number/business_unit_code/cash_register_code)
3. VAT system indicator (whether the issuer is in the VAT system)
4. Amount breakdown by tax rate
5. Payment method
6. Operator's OIB (tax number)
7. Issuer Protection Code (ZKI)

## Technical Architecture

The fiscalization process follows this model:

1. When issuing an invoice, the automated software extracts a set of data from the invoice and sends a fiscalization message to the Tax Administration
2. The Tax Administration generates the JIR and returns it to the cash register system
3. The cash register continues with invoice issuance, which now contains the JIR
4. Individuals can verify if the invoice has been reported to the Tax Administration

## API Endpoints

The fiscalization service provides the following endpoints:

1. **Invoice Request** - For standard invoice fiscalization
2. **Invoice Supporting Document Request** - For invoices based on supporting documents
3. **Change Payment Method Request** - For changing payment method on already fiscalized invoices
4. **Supporting Document Request** - For fiscalizing supporting documents
5. **Fiscalization Status Request** - For checking fiscalization status

## Payment Methods

The following payment method codes are used:
- **G** - Cash (Gotovina)
- **K** - Cards (Kartice)
- **C** - Check (�ek)
- **T** - Bank transfer (Transakcijski ra�un)
- **O** - Other (Ostalo)

Note: For multiple payment methods on a single invoice, use 'O' (Other).

## Error Handling

The service uses two error status types:
- **T** - Fatal error (service failed to receive the request, client must retry)
- **F** - Fiscalization error (service received the request but fiscalization failed, service will retry within specified timeframe)

## Implementation Notes

- The service must be integrated with the Bank's architecture as agreed with the Bank's IT department
- Invoices are currently generated in Hyperion and HAS applications and sent to clients via email in PDF format
- The fiscalization service will add the required JIR, ZKI, and QR code to these invoices

## Regulatory Compliance

This implementation is based on the draft of the Croatian Fiscalization Law. Changes may occur before and after the law is officially published in the Official Gazette (Narodne novine).

**Implementation deadline: January 1, 2026**